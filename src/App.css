@import "tailwindcss";

@font-face {
  font-family: 'Woodblock';
  src: url('/woodblock.otf') format('opentype');
  font-display: swap;
}

@font-face {
  font-family: 'Futura';
  src: url('/Futura.woff2') format('woff2'),
      url('/Futura.woff') format('woff'),
      url('/Futura.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

/* Base styles */
html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Futura', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  font-optical-sizing: auto;
  font-style: normal;
  @apply bg-slate-950 text-slate-100 antialiased;
}

/* Typography */
.woodblock {
  font-family: 'Woodblock', sans-serif;
}

/* Form elements */
.input {
  @apply bg-slate-800/80 text-slate-100 border-slate-600/50 rounded-lg p-3 outline-none transition-all border focus:border-yellow-400 focus:ring-2 focus:ring-yellow-400/20 placeholder-slate-400;
}

/* Container utility */
.container {
  @apply max-w-7xl mx-auto;
}

/* Accessibility utilities */
.sr-only {
  @apply absolute w-px h-px p-0 -m-px overflow-hidden whitespace-nowrap border-0;
}

.focus\:not-sr-only:focus {
  @apply static w-auto h-auto p-4 m-0 overflow-visible whitespace-normal;
}

/* Custom hover states */
.hover-lift {
  @apply transition-transform duration-200 hover:-translate-y-1;
}

/* Gradient text utility */
.gradient-text {
  @apply bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-slate-900;
}

::-webkit-scrollbar-thumb {
  @apply bg-slate-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-slate-500;
}

/* Focus styles for better accessibility */
button:focus-visible,
a:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible {
  @apply outline-2 outline-offset-2 outline-yellow-400;
}

/* Animation utilities */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

.animate-pulse-slow {
  animation: pulse 2s ease-in-out infinite;
}

/* Enhanced visual elements */
.glass-effect {
  @apply bg-slate-800/40 backdrop-blur-lg border border-slate-700/60 shadow-xl;
}

.section-divider {
  @apply border-t border-slate-700/30 pt-4 mt-4;
}

/* Improved preset button styling */
.preset-button {
  @apply w-10 h-10 rounded-lg cursor-pointer hover:-translate-y-0.5 transition-all border-2 flex items-center justify-center shadow-sm;
}

.preset-button.selected {
  @apply border-yellow-400 shadow-yellow-400/20;
}

.preset-button:not(.selected) {
  @apply border-slate-600/50 hover:border-slate-500;
}

/* Toolbar content distribution */
.toolbar-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: space-between;
}

.toolbar-scrollable {
  flex: 1;
  overflow-y: auto;
  padding-right: 0.25rem;
}

.toolbar-footer {
  margin-top: auto;
  padding-top: 1rem;
}

.icon-accent {
  @apply text-yellow-400;
}

.card-hover {
  @apply transition-all duration-200 hover:shadow-lg hover:shadow-slate-900/20 hover:-translate-y-0.5;
}

/* Optimized layout for perfect visual balance */
.editor-layout {
  display: grid;
  grid-template-columns: 3fr 2.2fr; /* Enlarged preview with balanced form area */
  gap: 1.75rem; /* Optimal spacing */
  align-items: stretch; /* Ensure height matching */
  max-width: 98rem; /* Maximum screen utilization */
  margin: 0 auto;
  padding: 0 0.75rem; /* Minimal padding to push preview left */
  min-height: 600px; /* Ensure consistent minimum height */
}

.preview-section {
  position: sticky;
  top: 2rem;
  justify-self: start; /* Align preview to the left */
  width: 100%;
  display: flex;
  flex-direction: column;
  height: fit-content;
}

.preview-container {
  width: 100%;
  height: auto; /* Let aspect ratio determine height */
  aspect-ratio: 16/9;
  border-radius: 0.75rem;
  overflow: hidden;
  background: #0f172a;
  border: 1px solid rgba(71, 85, 105, 0.5);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
  margin: 0; /* No margins for maximum left positioning */
  transform: translateX(-0.5rem); /* Subtle left shift for better positioning */
}

.toolbar-section {
  position: sticky;
  top: 2rem;
  height: fit-content;
  min-height: 650px; /* Match preview minimum height */
  max-height: 80vh; /* Match preview max height */
  width: 100%;
  min-width: 350px; /* Adequate width for form elements */
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}

/* Large screen optimizations */
@media (min-width: 1400px) {
  .editor-layout {
    grid-template-columns: 3.2fr 2.3fr; /* Maximum preview prominence on large screens */
    gap: 2rem; /* More breathing room */
    max-width: 105rem; /* Full utilization of large screens */
  }

  .preview-container {
    min-height: 700px; /* Larger preview on big screens */
    transform: translateX(-1rem); /* More pronounced left shift */
  }

  .toolbar-section {
    min-height: 700px; /* Match larger preview */
  }
}

/* Medium screen optimizations */
@media (min-width: 1200px) and (max-width: 1399px) {
  .editor-layout {
    grid-template-columns: 3.1fr 2.25fr; /* Balanced for medium-large screens */
    gap: 1.85rem;
  }
}

/* Tablet and mobile responsive adjustments */
@media (max-width: 1024px) {
  .editor-layout {
    grid-template-columns: 1fr;
    gap: 1.5rem;
    padding: 0 0.5rem;
    min-height: auto;
  }

  .preview-section,
  .toolbar-section {
    position: static;
    min-height: auto;
  }

  .preview-container {
    min-height: 300px; /* Compact but visible on mobile */
    max-height: 60vh; /* Prevent mobile overflow */
    transform: none; /* Remove transform on mobile */
  }
}

/* Small mobile adjustments */
@media (max-width: 640px) {
  .editor-layout {
    padding: 0 0.25rem;
    gap: 1rem;
  }

  .preview-container {
    min-height: 250px; /* Very compact on small screens */
  }
}

/* Additional responsive improvements */
@media (max-width: 1024px) {
  .container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (max-width: 768px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* 移动端工具栏优化 */
  .mobile-toolbar {
    max-height: none;
    overflow: visible;
  }

  .mobile-toolbar .flex-1 {
    flex: none; /* Disable flex-1 on mobile for better content flow */
  }

  /* 移动端预设主题网格 */
  .mobile-preset-grid {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  /* 移动端颜色选择器网格 - 更紧凑的布局 */
  .mobile-color-grid {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  /* 移动端预设按钮优化 */
  .preset-button {
    width: 2rem;
    height: 2rem;
  }

  /* 移动端输入框优化 */
  .input {
    padding: 0.625rem;
    font-size: 1rem;
  }
}

@media (max-width: 640px) {
  /* 超小屏幕优化 */
  .mobile-color-grid {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}